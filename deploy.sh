#!/bin/bash

# Supabase Kubernetes Deployment Script
# This script deploys a complete self-hosted Supabase stack to Kubernetes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    print_success "kubectl is available"
}

# Check if cluster is accessible
check_cluster() {
    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    print_success "Kubernetes cluster is accessible"
}

# Deploy namespace and basic configuration
deploy_base() {
    print_status "Deploying namespace and basic configuration..."
    kubectl apply -f k8s/base/namespace.yaml
    kubectl apply -f k8s/base/secrets.yaml
    kubectl apply -f k8s/base/configmap.yaml
    print_success "Base configuration deployed"
}

# Deploy PostgreSQL initialization
deploy_postgres_init() {
    print_status "Deploying PostgreSQL initialization scripts..."
    kubectl apply -f k8s/services/postgres-init/init-configmap.yaml
    print_success "PostgreSQL initialization scripts deployed"
}

# Deploy core services
deploy_services() {
    print_status "Deploying core services..."
    
    # Deploy Vector first (dependency for PostgreSQL)
    kubectl apply -f k8s/services/vector.yaml
    
    # Deploy PostgreSQL
    kubectl apply -f k8s/services/postgres.yaml
    
    # Wait for PostgreSQL to be ready
    print_status "Waiting for PostgreSQL to be ready..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=supabase-db -n supabase --timeout=300s
    
    # Run PostgreSQL initialization
    kubectl apply -f k8s/services/postgres-init/init-job.yaml
    
    # Wait for initialization to complete
    print_status "Waiting for PostgreSQL initialization to complete..."
    kubectl wait --for=condition=complete job/postgres-init -n supabase --timeout=300s
    
    # Deploy Analytics (dependency for other services)
    kubectl apply -f k8s/services/analytics.yaml
    
    # Wait for Analytics to be ready
    print_status "Waiting for Analytics to be ready..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=supabase-analytics -n supabase --timeout=300s
    
    # Deploy other services
    kubectl apply -f k8s/services/auth.yaml
    kubectl apply -f k8s/services/rest.yaml
    kubectl apply -f k8s/services/storage.yaml
    kubectl apply -f k8s/services/realtime.yaml
    kubectl apply -f k8s/services/meta.yaml
    kubectl apply -f k8s/services/studio.yaml
    kubectl apply -f k8s/services/functions.yaml
    kubectl apply -f k8s/services/supavisor.yaml
    
    print_success "All services deployed"
}

# Deploy ingress
deploy_ingress() {
    print_status "Deploying ingress configuration..."
    kubectl apply -f k8s/base/ingress.yaml
    print_success "Ingress configuration deployed"
}

# Wait for all services to be ready
wait_for_services() {
    print_status "Waiting for all services to be ready..."
    
    services=(
        "supabase-analytics"
        "supabase-auth"
        "supabase-rest"
        "supabase-storage"
        "supabase-realtime"
        "supabase-meta"
        "supabase-studio"
        "supabase-functions"
        "supabase-supavisor"
        "kong"
    )
    
    for service in "${services[@]}"; do
        print_status "Waiting for $service to be ready..."
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=$service -n supabase --timeout=300s || true
    done
    
    print_success "All services are ready"
}

# Display access information
show_access_info() {
    print_success "Supabase deployment completed!"
    echo ""
    echo "Access Information:"
    echo "=================="
    
    # Get LoadBalancer IP or use localhost for local development
    KONG_IP=$(kubectl get svc supabase-kong -n supabase -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "localhost")
    if [ "$KONG_IP" = "localhost" ]; then
        KONG_PORT=$(kubectl get svc supabase-kong -n supabase -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "8000")
        KONG_URL="http://localhost:$KONG_PORT"
    else
        KONG_URL="http://$KONG_IP:8000"
    fi
    
    echo "🌐 API Gateway (Kong): $KONG_URL"
    echo "📊 Studio Dashboard: $KONG_URL (with basic auth)"
    echo "📈 Analytics: http://analytics.supabase.local (if using ingress)"
    echo ""
    echo "Default Credentials:"
    echo "==================="
    echo "Dashboard Username: supabase"
    echo "Dashboard Password: this_password_is_insecure_and_should_be_updated"
    echo ""
    echo "API Keys:"
    echo "========="
    echo "Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE"
    echo ""
    print_warning "IMPORTANT: Change all default passwords and secrets before using in production!"
}

# Main deployment function
main() {
    print_status "Starting Supabase Kubernetes deployment..."
    
    check_kubectl
    check_cluster
    deploy_base
    deploy_postgres_init
    deploy_services
    deploy_ingress
    wait_for_services
    show_access_info
}

# Run main function
main "$@"
