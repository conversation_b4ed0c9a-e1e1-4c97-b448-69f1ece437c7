apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init-scripts
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-db
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: supabase
data:
  99-roles.sql: |
    -- Create roles for Supabase
    CREATE ROLE anon NOLOGIN NOINHERIT;
    CREATE ROLE authenticated NOLOGIN NOINHERIT;
    CREATE ROLE service_role NOLOGIN NOINHERIT BYPASSRLS;
    CREATE ROLE supabase_admin NOINHERIT CREATEROLE CREATEDB REPLICATION LOGIN PASSWORD 'your-super-secret-and-long-postgres-password';
    CREATE ROLE supabase_auth_admin NOINHERIT CREATEROLE CREATEDB LOGIN PASSWORD 'your-super-secret-and-long-postgres-password';
    CREATE ROLE supabase_storage_admin NOINHERIT CREATEROLE CREATEDB LOGIN PASSWORD 'your-super-secret-and-long-postgres-password';

    -- Update existing roles to allow login
    ALTER ROLE supabase_admin LOGIN;
    ALTER ROLE supabase_auth_admin LOGIN;
    ALTER ROLE supabase_storage_admin LOGIN;
    CREATE ROLE dashboard_user NOINHERIT CREATEDB;
    CREATE ROLE authenticator NOINHERIT LOGIN PASSWORD 'your-super-secret-and-long-postgres-password';

    -- Grant permissions
    GRANT anon TO authenticator;
    GRANT authenticated TO authenticator;
    GRANT service_role TO authenticator;
    GRANT supabase_admin TO postgres;

    -- Set default privileges
    ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON TABLES TO postgres, anon, authenticated, service_role;
    ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON FUNCTIONS TO postgres, anon, authenticated, service_role;
    ALTER DEFAULT PRIVILEGES FOR ROLE supabase_admin IN SCHEMA public GRANT ALL ON SEQUENCES TO postgres, anon, authenticated, service_role;

  99-jwt.sql: |
    -- Initialize JWT settings
    ALTER DATABASE postgres SET "app.settings.jwt_secret" TO 'your-super-secret-jwt-token-with-at-least-32-characters-long';
    ALTER DATABASE postgres SET "app.settings.jwt_exp" TO '3600';

  97-_supabase.sql: |
    -- Create _supabase database for internal data
    CREATE DATABASE _supabase;
    \c _supabase;
    
    -- Create schemas
    CREATE SCHEMA IF NOT EXISTS _analytics;
    CREATE SCHEMA IF NOT EXISTS _realtime;
    
    -- Grant permissions
    GRANT USAGE ON SCHEMA _analytics TO supabase_admin;
    GRANT USAGE ON SCHEMA _realtime TO supabase_admin;
    GRANT ALL ON ALL TABLES IN SCHEMA _analytics TO supabase_admin;
    GRANT ALL ON ALL TABLES IN SCHEMA _realtime TO supabase_admin;

  99-logs.sql: |
    -- Create logs schema and tables for analytics
    \c _supabase;
    
    CREATE SCHEMA IF NOT EXISTS _analytics;
    
    -- Create basic tables for log storage
    CREATE TABLE IF NOT EXISTS _analytics.logs (
      id BIGSERIAL PRIMARY KEY,
      timestamp TIMESTAMPTZ DEFAULT NOW(),
      level TEXT,
      message TEXT,
      metadata JSONB
    );
    
    -- Grant permissions
    GRANT ALL ON SCHEMA _analytics TO supabase_admin;
    GRANT ALL ON ALL TABLES IN SCHEMA _analytics TO supabase_admin;
    GRANT ALL ON ALL SEQUENCES IN SCHEMA _analytics TO supabase_admin;

  99-pooler.sql: |
    -- Create pooler configuration
    \c _supabase;
    
    CREATE SCHEMA IF NOT EXISTS _pooler;
    
    -- Create pooler tables
    CREATE TABLE IF NOT EXISTS _pooler.tenants (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    
    -- Grant permissions
    GRANT ALL ON SCHEMA _pooler TO supabase_admin;
    GRANT ALL ON ALL TABLES IN SCHEMA _pooler TO supabase_admin;
    GRANT ALL ON ALL SEQUENCES IN SCHEMA _pooler TO supabase_admin;
