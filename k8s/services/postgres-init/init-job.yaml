apiVersion: batch/v1
kind: Job
metadata:
  name: postgres-init
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-db-init
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: supabase
spec:
  ttlSecondsAfterFinished: 100
  template:
    metadata:
      labels:
        app.kubernetes.io/name: supabase-db-init
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: database
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
      - name: postgres-init
        image: postgres:15-alpine
        command:
        - /bin/sh
        - -c
        - |
          # Wait for PostgreSQL to be ready
          until PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U postgres -c '\q'; do
            echo "PostgreSQL is unavailable - sleeping"
            sleep 2
          done
          
          echo "PostgreSQL is up - executing initialization scripts"
          
          # Execute initialization scripts
          PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U postgres -f /docker-entrypoint-initdb.d/99-roles.sql
          PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U postgres -f /docker-entrypoint-initdb.d/99-jwt.sql
          PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U postgres -f /docker-entrypoint-initdb.d/97-_supabase.sql
          PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U postgres -f /docker-entrypoint-initdb.d/99-logs.sql
          PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -U postgres -f /docker-entrypoint-initdb.d/99-pooler.sql
          
          echo "Initialization complete"
        env:
        - name: POSTGRES_HOST
          valueFrom:
            configMapKeyRef:
              name: supabase-config
              key: POSTGRES_HOST
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: supabase-secrets
              key: POSTGRES_PASSWORD
        volumeMounts:
        - name: init-scripts
          mountPath: /docker-entrypoint-initdb.d
      volumes:
      - name: init-scripts
        configMap:
          name: postgres-init-scripts
      restartPolicy: OnFailure
