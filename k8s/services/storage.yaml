---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supabase-storage-data
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-storage
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: storage
    app.kubernetes.io/part-of: supabase
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: hostpath

---
apiVersion: v1
kind: Service
metadata:
  name: supabase-storage
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-storage
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: storage
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 5000
      targetPort: 5000
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: supabase-storage
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-storage
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-storage
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: storage
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.kubernetes.io/name: supabase-storage
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: supabase-storage
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: storage
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: storage
          image: supabase/storage-api:v1.24.7
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 5000
              name: http
          env:
            - name: ANON_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: ANON_KEY
            - name: SERVICE_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SERVICE_ROLE_KEY
            - name: POSTGREST_URL
              value: "http://supabase-rest:3000"
            - name: PGRST_JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: JWT_SECRET
            - name: DATABASE_URL
              value: "postgres://supabase_storage_admin:$(POSTGRES_PASSWORD)@$(POSTGRES_HOST):$(POSTGRES_PORT)/$(POSTGRES_DB)"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: POSTGRES_PASSWORD
            - name: POSTGRES_HOST
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_HOST
            - name: POSTGRES_PORT
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_PORT
            - name: POSTGRES_DB
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_DB
            - name: FILE_SIZE_LIMIT
              value: "52428800"
            - name: STORAGE_BACKEND
              value: "file"
            - name: FILE_STORAGE_BACKEND_PATH
              value: "/var/lib/storage"
            - name: TENANT_ID
              value: "stub"
            - name: REGION
              value: "stub"
            - name: GLOBAL_S3_BUCKET
              value: "stub"
            - name: ENABLE_IMAGE_TRANSFORMATION
              value: "true"
            - name: IMGPROXY_URL
              value: "http://supabase-imgproxy:5001"
          volumeMounts:
            - name: storage-data
              mountPath: /var/lib/storage
          livenessProbe:
            httpGet:
              path: /status
              port: 5000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /status
              port: 5000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "256Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
      volumes:
        - name: storage-data
          persistentVolumeClaim:
            claimName: supabase-storage-data
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: supabase-imgproxy
  namespace: supabase
  labels:
    app.kubernetes.io/name: imgproxy
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: storage
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 5001
      targetPort: 5001
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: imgproxy
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-imgproxy
  namespace: supabase
  labels:
    app.kubernetes.io/name: imgproxy
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: storage
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: imgproxy
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: imgproxy
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: storage
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: imgproxy
          image: darthsim/imgproxy:v3.8.0
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 5001
              name: http
          env:
            - name: IMGPROXY_BIND
              value: ":5001"
            - name: IMGPROXY_LOCAL_FILESYSTEM_ROOT
              value: "/"
            - name: IMGPROXY_USE_ETAG
              value: "true"
            - name: IMGPROXY_ENABLE_WEBP_DETECTION
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: IMGPROXY_ENABLE_WEBP_DETECTION
          volumeMounts:
            - name: storage-data
              mountPath: /var/lib/storage
          livenessProbe:
            exec:
              command:
                - imgproxy
                - health
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            exec:
              command:
                - imgproxy
                - health
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
      volumes:
        - name: storage-data
          persistentVolumeClaim:
            claimName: supabase-storage-data
      restartPolicy: Always
