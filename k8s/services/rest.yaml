---
apiVersion: v1
kind: Service
metadata:
  name: supabase-rest
  namespace: supabase
  labels:
    app.kubernetes.io/name: postgrest
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: api
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: postgrest
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-rest
  namespace: supabase
  labels:
    app.kubernetes.io/name: postgrest
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: api
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: postgrest
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgrest
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: api
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: postgrest
          image: postgrest/postgrest:v12.2.12
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
              name: http
          env:
            - name: PGRST_DB_URI
              value: "postgres://authenticator:$(POSTGRES_PASSWORD)@$(POSTGRES_HOST):$(POSTGRES_PORT)/$(POSTGRES_DB)"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: POSTGRES_PASSWORD
            - name: POSTGRES_HOST
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_HOST
            - name: POSTGRES_PORT
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_PORT
            - name: POSTGRES_DB
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_DB
            - name: PGRST_DB_SCHEMAS
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: PGRST_DB_SCHEMAS
            - name: PGRST_DB_ANON_ROLE
              value: "anon"
            - name: PGRST_JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: JWT_SECRET
            - name: PGRST_DB_USE_LEGACY_GUCS
              value: "false"
            - name: PGRST_APP_SETTINGS_JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: JWT_SECRET
            - name: PGRST_APP_SETTINGS_JWT_EXP
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: JWT_EXPIRY
          command:
            - postgrest
          livenessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
      restartPolicy: Always
