---
apiVersion: v1
kind: Service
metadata:
  name: supabase-studio
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-studio
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: dashboard
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: supabase-studio
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-studio
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-studio
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: dashboard
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: supabase-studio
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: supabase-studio
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: dashboard
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: studio
          image: supabase/studio:2025.06.30-sha-6f5982d
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
              name: http
          env:
            - name: STUDIO_PG_META_URL
              value: "http://supabase-meta:8080"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: POSTGRES_PASSWORD
            - name: DEFAULT_ORGANIZATION_NAME
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: STUDIO_DEFAULT_ORGANIZATION
            - name: DEFAULT_PROJECT_NAME
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: STUDIO_DEFAULT_PROJECT
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: OPENAI_API_KEY
            - name: SUPABASE_URL
              value: "http://supabase-kong:8000"
            - name: SUPABASE_PUBLIC_URL
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: SUPABASE_PUBLIC_URL
            - name: SUPABASE_ANON_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: ANON_KEY
            - name: SUPABASE_SERVICE_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SERVICE_ROLE_KEY
            - name: AUTH_JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: JWT_SECRET
            - name: LOGFLARE_PRIVATE_ACCESS_TOKEN
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: LOGFLARE_PRIVATE_ACCESS_TOKEN
            - name: LOGFLARE_URL
              value: "http://supabase-analytics:4000"
            - name: NEXT_PUBLIC_ENABLE_LOGS
              value: "true"
            - name: NEXT_ANALYTICS_BACKEND_PROVIDER
              value: "postgres"
          livenessProbe:
            exec:
              command:
                - node
                - -e
                - "fetch('http://studio:3000/api/platform/profile').then((r) => {if (r.status !== 200) throw new Error(r.status)})"
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /api/platform/profile
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
      restartPolicy: Always
