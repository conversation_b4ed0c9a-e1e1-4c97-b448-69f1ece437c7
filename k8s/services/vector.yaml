---
apiVersion: v1
kind: ConfigMap
metadata:
  name: vector-config
  namespace: supabase
  labels:
    app.kubernetes.io/name: vector
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: logging
    app.kubernetes.io/part-of: supabase
data:
  vector.yml: |
    api:
      enabled: true
      address: 0.0.0.0:9001
      playground: false

    sources:
      docker_logs:
        type: docker_logs
        docker_host: unix:///var/run/docker.sock
        include_containers:
          - supabase-*

    transforms:
      parse_logs:
        type: remap
        inputs:
          - docker_logs
        source: |
          .timestamp = now()
          .level = "info"

    sinks:
      logflare:
        type: http
        inputs:
          - parse_logs
        uri: http://supabase-analytics:4000/api/logs
        method: post
        auth:
          strategy: bearer
          token: "${LOGFLARE_PUBLIC_ACCESS_TOKEN}"
        encoding:
          codec: json
        healthcheck:
          enabled: true

---
apiVersion: v1
kind: Service
metadata:
  name: supabase-vector
  namespace: supabase
  labels:
    app.kubernetes.io/name: vector
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: logging
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 9001
      targetPort: 9001
      protocol: TCP
      name: api
  selector:
    app.kubernetes.io/name: vector
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-vector
  namespace: supabase
  labels:
    app.kubernetes.io/name: vector
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: logging
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: vector
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: vector
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: logging
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: vector
          image: timberio/vector:0.28.1-alpine
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 9001
              name: api
          env:
            - name: LOGFLARE_PUBLIC_ACCESS_TOKEN
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: LOGFLARE_PUBLIC_ACCESS_TOKEN
          command:
            - vector
            - --config
            - /etc/vector/vector.yml
          volumeMounts:
            - name: vector-config
              mountPath: /etc/vector
              readOnly: true
            - name: docker-socket
              mountPath: /var/run/docker.sock
              readOnly: true
          livenessProbe:
            httpGet:
              path: /health
              port: 9001
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: 9001
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          securityContext:
            runAsNonRoot: false
            runAsUser: 0
      volumes:
        - name: vector-config
          configMap:
            name: vector-config
        - name: docker-socket
          hostPath:
            path: /var/run/docker.sock
            type: Socket
      restartPolicy: Always
