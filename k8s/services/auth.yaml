---
apiVersion: v1
kind: Service
metadata:
  name: supabase-auth
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-auth
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: auth
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 9999
      targetPort: 9999
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: supabase-auth
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-auth
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-auth
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: auth
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: supabase-auth
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: supabase-auth
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: auth
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: gotrue
          image: supabase/gotrue:v2.176.1
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 9999
              name: http
          env:
            - name: GOTRUE_API_HOST
              value: "0.0.0.0"
            - name: GOTRUE_API_PORT
              value: "9999"
            - name: API_EXTERNAL_URL
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: API_EXTERNAL_URL
            - name: GOTRUE_DB_DRIVER
              value: "postgres"
            - name: GOTRUE_DB_DATABASE_URL
              value: "postgres://supabase_auth_admin:$(POSTGRES_PASSWORD)@$(POSTGRES_HOST):$(POSTGRES_PORT)/$(POSTGRES_DB)"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: POSTGRES_PASSWORD
            - name: POSTGRES_HOST
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_HOST
            - name: POSTGRES_PORT
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_PORT
            - name: POSTGRES_DB
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_DB
            - name: GOTRUE_SITE_URL
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: SITE_URL
            - name: GOTRUE_URI_ALLOW_LIST
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: ADDITIONAL_REDIRECT_URLS
            - name: GOTRUE_DISABLE_SIGNUP
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: DISABLE_SIGNUP
            - name: GOTRUE_JWT_ADMIN_ROLES
              value: "service_role"
            - name: GOTRUE_JWT_AUD
              value: "authenticated"
            - name: GOTRUE_JWT_DEFAULT_GROUP_NAME
              value: "authenticated"
            - name: GOTRUE_JWT_EXP
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: JWT_EXPIRY
            - name: GOTRUE_JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: JWT_SECRET
            - name: GOTRUE_EXTERNAL_EMAIL_ENABLED
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: ENABLE_EMAIL_SIGNUP
            - name: GOTRUE_EXTERNAL_ANONYMOUS_USERS_ENABLED
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: ENABLE_ANONYMOUS_USERS
            - name: GOTRUE_MAILER_AUTOCONFIRM
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: ENABLE_EMAIL_AUTOCONFIRM
            - name: GOTRUE_SMTP_ADMIN_EMAIL
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SMTP_ADMIN_EMAIL
            - name: GOTRUE_SMTP_HOST
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SMTP_HOST
            - name: GOTRUE_SMTP_PORT
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SMTP_PORT
            - name: GOTRUE_SMTP_USER
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SMTP_USER
            - name: GOTRUE_SMTP_PASS
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SMTP_PASS
            - name: GOTRUE_SMTP_SENDER_NAME
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SMTP_SENDER_NAME
            - name: GOTRUE_MAILER_URLPATHS_INVITE
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: MAILER_URLPATHS_INVITE
            - name: GOTRUE_MAILER_URLPATHS_CONFIRMATION
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: MAILER_URLPATHS_CONFIRMATION
            - name: GOTRUE_MAILER_URLPATHS_RECOVERY
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: MAILER_URLPATHS_RECOVERY
            - name: GOTRUE_MAILER_URLPATHS_EMAIL_CHANGE
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: MAILER_URLPATHS_EMAIL_CHANGE
            - name: GOTRUE_EXTERNAL_PHONE_ENABLED
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: ENABLE_PHONE_SIGNUP
            - name: GOTRUE_SMS_AUTOCONFIRM
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: ENABLE_PHONE_AUTOCONFIRM
          livenessProbe:
            httpGet:
              path: /health
              port: 9999
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: 9999
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
      restartPolicy: Always
