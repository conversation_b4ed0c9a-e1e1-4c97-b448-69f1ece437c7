---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kong-config
  namespace: supabase
  labels:
    app.kubernetes.io/name: kong
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: gateway
    app.kubernetes.io/part-of: supabase
data:
  kong.yml: |
    _format_version: "1.1"
    
    consumers:
      - username: DASHBOARD
      - username: anon
        keyauth_credentials:
          - key: ${ANON_KEY}
      - username: service_role
        keyauth_credentials:
          - key: ${SERVICE_ROLE_KEY}
    
    acls:
      - consumer: anon
        group: anon
      - consumer: service_role
        group: admin
    
    services:
      - name: auth-v1-open
        url: http://supabase-auth:9999/
        routes:
          - name: auth-v1-open
            strip_path: true
            paths:
              - /auth/v1/verify
        plugins:
          - name: cors
      - name: auth-v1-open-callback
        url: http://supabase-auth:9999/callback
        routes:
          - name: auth-v1-open-callback
            strip_path: true
            paths:
              - /auth/v1/callback
        plugins:
          - name: cors
      - name: auth-v1-open-authorize
        url: http://supabase-auth:9999/authorize
        routes:
          - name: auth-v1-open-authorize
            strip_path: true
            paths:
              - /auth/v1/authorize
        plugins:
          - name: cors
      - name: auth-v1
        _comment: "GoTrue: /auth/v1/* -> http://supabase-auth:9999/*"
        url: http://supabase-auth:9999/
        routes:
          - name: auth-v1-all
            strip_path: true
            paths:
              - /auth/v1/
        plugins:
          - name: cors
          - name: key-auth
            config:
              hide_credentials: false
          - name: acl
            config:
              hide_groups_header: true
              allow:
                - admin
                - anon
      - name: rest-v1
        _comment: "PostgREST: /rest/v1/* -> http://supabase-rest:3000/*"
        url: http://supabase-rest:3000/
        routes:
          - name: rest-v1-all
            strip_path: true
            paths:
              - /rest/v1/
        plugins:
          - name: cors
          - name: key-auth
            config:
              hide_credentials: true
          - name: acl
            config:
              hide_groups_header: true
              allow:
                - admin
                - anon
      - name: realtime-v1
        _comment: "Realtime: /realtime/v1/* -> ws://supabase-realtime:4000/socket/*"
        url: http://supabase-realtime:4000/socket/
        routes:
          - name: realtime-v1-all
            strip_path: true
            paths:
              - /realtime/v1/
        plugins:
          - name: cors
          - name: key-auth
            config:
              hide_credentials: false
          - name: acl
            config:
              hide_groups_header: true
              allow:
                - admin
                - anon
      - name: storage-v1
        _comment: "Storage: /storage/v1/* -> http://supabase-storage:5000/*"
        url: http://supabase-storage:5000/
        routes:
          - name: storage-v1-all
            strip_path: true
            paths:
              - /storage/v1/
        plugins:
          - name: cors
      - name: functions-v1
        _comment: "Edge Functions: /functions/v1/* -> http://supabase-functions:9000/*"
        url: http://supabase-functions:9000/
        routes:
          - name: functions-v1-all
            strip_path: true
            paths:
              - /functions/v1/
        plugins:
          - name: cors
      - name: dashboard
        _comment: "Dashboard: /* -> http://supabase-studio:3000/*"
        url: http://supabase-studio:3000/
        routes:
          - name: dashboard-all
            strip_path: false
            paths:
              - /
        plugins:
          - name: cors
          - name: basic-auth
            config:
              hide_credentials: true

    basicauth_credentials:
      - consumer: DASHBOARD
        username: ${DASHBOARD_USERNAME}
        password: ${DASHBOARD_PASSWORD}

---
apiVersion: v1
kind: Service
metadata:
  name: supabase-kong
  namespace: supabase
  labels:
    app.kubernetes.io/name: kong
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: gateway
    app.kubernetes.io/part-of: supabase
spec:
  type: LoadBalancer
  ports:
    - port: 8000
      targetPort: 8000
      protocol: TCP
      name: http
    - port: 8443
      targetPort: 8443
      protocol: TCP
      name: https
  selector:
    app.kubernetes.io/name: kong
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-kong
  namespace: supabase
  labels:
    app.kubernetes.io/name: kong
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: gateway
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: kong
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: kong
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: gateway
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: kong
          image: kong:2.8.1
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8000
              name: http
            - containerPort: 8443
              name: https
          env:
            - name: KONG_DATABASE
              value: "off"
            - name: KONG_DECLARATIVE_CONFIG
              value: "/home/<USER>/kong.yml"
            - name: KONG_DNS_ORDER
              value: "LAST,A,CNAME"
            - name: KONG_PLUGINS
              value: "request-transformer,cors,key-auth,acl,basic-auth"
            - name: KONG_NGINX_PROXY_PROXY_BUFFER_SIZE
              value: "160k"
            - name: KONG_NGINX_PROXY_PROXY_BUFFERS
              value: "64 160k"
            - name: SUPABASE_ANON_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: ANON_KEY
            - name: SUPABASE_SERVICE_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SERVICE_ROLE_KEY
            - name: DASHBOARD_USERNAME
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: DASHBOARD_USERNAME
            - name: DASHBOARD_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: DASHBOARD_PASSWORD
            - name: ANON_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: ANON_KEY
            - name: SERVICE_ROLE_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SERVICE_ROLE_KEY
          command:
            - /bin/bash
            - -c
            - |
              eval "echo \"$(cat /home/<USER>/temp.yml)\"" > /home/<USER>/kong.yml && /docker-entrypoint.sh kong docker-start
          volumeMounts:
            - name: kong-config
              mountPath: /home/<USER>/temp.yml
              subPath: kong.yml
              readOnly: true
          livenessProbe:
            httpGet:
              path: /status
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /status
              port: 8000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "256Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
      volumes:
        - name: kong-config
          configMap:
            name: kong-config
      restartPolicy: Always
