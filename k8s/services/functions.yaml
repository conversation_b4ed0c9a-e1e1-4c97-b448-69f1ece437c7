---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supabase-functions-data
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-functions
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: functions
    app.kubernetes.io/part-of: supabase
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: hostpath

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: functions-main
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-functions
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: functions
    app.kubernetes.io/part-of: supabase
data:
  index.ts: |
    // Default main function for Supabase Edge Functions
    import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

    serve(async (req) => {
      const { pathname } = new URL(req.url)
      
      // Route to different functions based on pathname
      if (pathname === '/hello') {
        return new Response(
          JSON.stringify({ message: 'Hello from Supabase Edge Functions!' }),
          { headers: { "Content-Type": "application/json" } }
        )
      }
      
      return new Response(
        JSON.stringify({ error: 'Function not found' }),
        { status: 404, headers: { "Content-Type": "application/json" } }
      )
    })

---
apiVersion: v1
kind: Service
metadata:
  name: supabase-functions
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-functions
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: functions
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 9000
      targetPort: 9000
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: supabase-functions
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-functions
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-functions
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: functions
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: supabase-functions
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: supabase-functions
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: functions
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: edge-runtime
          image: supabase/edge-runtime:v1.67.4
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 9000
              name: http
          env:
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: JWT_SECRET
            - name: SUPABASE_URL
              value: "http://supabase-kong:8000"
            - name: SUPABASE_ANON_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: ANON_KEY
            - name: SUPABASE_SERVICE_ROLE_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SERVICE_ROLE_KEY
            - name: SUPABASE_DB_URL
              value: "postgresql://postgres:$(POSTGRES_PASSWORD)@$(POSTGRES_HOST):$(POSTGRES_PORT)/$(POSTGRES_DB)"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: POSTGRES_PASSWORD
            - name: POSTGRES_HOST
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_HOST
            - name: POSTGRES_PORT
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_PORT
            - name: POSTGRES_DB
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_DB
            - name: VERIFY_JWT
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: FUNCTIONS_VERIFY_JWT
          command:
            - start
            - --main-service
            - /home/<USER>/functions/main
          volumeMounts:
            - name: functions-data
              mountPath: /home/<USER>/functions
            - name: main-function
              mountPath: /home/<USER>/functions/main
              subPath: index.ts
          livenessProbe:
            httpGet:
              path: /
              port: 9000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: 9000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "256Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
      volumes:
        - name: functions-data
          persistentVolumeClaim:
            claimName: supabase-functions-data
        - name: main-function
          configMap:
            name: functions-main
      restartPolicy: Always
