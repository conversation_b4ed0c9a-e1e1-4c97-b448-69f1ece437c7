---
apiVersion: v1
kind: Service
metadata:
  name: supabase-realtime
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-realtime
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: realtime
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 4000
      targetPort: 4000
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: supabase-realtime
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-realtime
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-realtime
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: realtime
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: supabase-realtime
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: supabase-realtime
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: realtime
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: realtime
          image: supabase/realtime:v2.34.47
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 4000
              name: http
          env:
            - name: PORT
              value: "4000"
            - name: DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_HOST
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_PORT
            - name: DB_USER
              value: "supabase_admin"
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: POSTGRES_PASSWORD
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_DB
            - name: DB_AFTER_CONNECT_QUERY
              value: "SET search_path TO _realtime"
            - name: DB_ENC_KEY
              value: "supabaserealtime"
            - name: API_JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: JWT_SECRET
            - name: SECRET_KEY_BASE
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SECRET_KEY_BASE
            - name: ERL_AFLAGS
              value: "-proto_dist inet_tcp"
            - name: DNS_NODES
              value: "''"
            - name: RLIMIT_NOFILE
              value: "10000"
            - name: APP_NAME
              value: "realtime"
            - name: SEED_SELF_HOST
              value: "true"
            - name: RUN_JANITOR
              value: "true"
          livenessProbe:
            httpGet:
              path: /api/tenants/realtime-dev/health
              port: 4000
              httpHeaders:
                - name: Authorization
                  value: "Bearer $(ANON_KEY)"
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
            env:
              - name: ANON_KEY
                valueFrom:
                  secretKeyRef:
                    name: supabase-secrets
                    key: ANON_KEY
          readinessProbe:
            httpGet:
              path: /api/tenants/realtime-dev/health
              port: 4000
              httpHeaders:
                - name: Authorization
                  value: "Bearer $(ANON_KEY)"
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
            env:
              - name: ANON_KEY
                valueFrom:
                  secretKeyRef:
                    name: supabase-secrets
                    key: ANON_KEY
          resources:
            requests:
              memory: "256Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
      restartPolicy: Always
