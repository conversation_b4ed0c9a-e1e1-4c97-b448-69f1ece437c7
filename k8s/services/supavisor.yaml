---
apiVersion: v1
kind: ConfigMap
metadata:
  name: supavisor-config
  namespace: supabase
  labels:
    app.kubernetes.io/name: supavisor
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: pooler
    app.kubernetes.io/part-of: supabase
data:
  pooler.exs: |
    # Supavisor configuration
    import Config

    config :supavisor,
      port: 4000,
      postgres_port: 5432,
      postgres_db: System.get_env("POSTGRES_DB"),
      postgres_password: System.get_env("POSTGRES_PASSWORD"),
      database_url: System.get_env("DATABASE_URL"),
      cluster_postgres: true,
      secret_key_base: System.get_env("SECRET_KEY_BASE"),
      vault_enc_key: System.get_env("VAULT_ENC_KEY"),
      api_jwt_secret: System.get_env("API_JWT_SECRET"),
      metrics_jwt_secret: System.get_env("METRICS_JWT_SECRET"),
      region: "local",
      pooler_tenant_id: System.get_env("POOLER_TENANT_ID"),
      pooler_default_pool_size: String.to_integer(System.get_env("POOLER_DEFAULT_POOL_SIZE") || "20"),
      pooler_max_client_conn: String.to_integer(System.get_env("POOLER_MAX_CLIENT_CONN") || "100"),
      pooler_pool_mode: "transaction",
      db_pool_size: String.to_integer(System.get_env("DB_POOL_SIZE") || "5")

---
apiVersion: v1
kind: Service
metadata:
  name: supabase-supavisor
  namespace: supabase
  labels:
    app.kubernetes.io/name: supavisor
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: pooler
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 5432
      targetPort: 5432
      protocol: TCP
      name: postgres
    - port: 6543
      targetPort: 6543
      protocol: TCP
      name: transaction
    - port: 4000
      targetPort: 4000
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: supavisor
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-supavisor
  namespace: supabase
  labels:
    app.kubernetes.io/name: supavisor
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: pooler
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: supavisor
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: supavisor
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: pooler
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: supavisor
          image: supabase/supavisor:2.5.6
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 5432
              name: postgres
            - containerPort: 6543
              name: transaction
            - containerPort: 4000
              name: http
          env:
            - name: PORT
              value: "4000"
            - name: POSTGRES_PORT
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_PORT
            - name: POSTGRES_DB
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_DB
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: POSTGRES_PASSWORD
            - name: DATABASE_URL
              value: "ecto://supabase_admin:$(POSTGRES_PASSWORD)@$(POSTGRES_HOST):$(POSTGRES_PORT)/_supabase"
            - name: POSTGRES_HOST
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_HOST
            - name: CLUSTER_POSTGRES
              value: "true"
            - name: SECRET_KEY_BASE
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: SECRET_KEY_BASE
            - name: VAULT_ENC_KEY
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: VAULT_ENC_KEY
            - name: API_JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: JWT_SECRET
            - name: METRICS_JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: JWT_SECRET
            - name: REGION
              value: "local"
            - name: ERL_AFLAGS
              value: "-proto_dist inet_tcp"
            - name: POOLER_TENANT_ID
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POOLER_TENANT_ID
            - name: POOLER_DEFAULT_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POOLER_DEFAULT_POOL_SIZE
            - name: POOLER_MAX_CLIENT_CONN
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POOLER_MAX_CLIENT_CONN
            - name: POOLER_POOL_MODE
              value: "transaction"
            - name: DB_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POOLER_DB_POOL_SIZE
          command:
            - /bin/sh
            - -c
            - |
              /app/bin/migrate && /app/bin/supavisor eval "$(cat /etc/pooler/pooler.exs)" && /app/bin/server
          volumeMounts:
            - name: pooler-config
              mountPath: /etc/pooler
              readOnly: true
          livenessProbe:
            httpGet:
              path: /api/health
              port: 4000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 5
          readinessProbe:
            httpGet:
              path: /api/health
              port: 4000
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 5
          resources:
            requests:
              memory: "256Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
      volumes:
        - name: pooler-config
          configMap:
            name: supavisor-config
      restartPolicy: Always
