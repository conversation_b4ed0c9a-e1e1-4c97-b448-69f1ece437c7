---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supabase-db-data
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-db
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: supabase
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: hostpath # Use appropriate storage class for your cluster

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: supabase-db-config
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-db
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: supabase
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  storageClassName: hostpath

---
apiVersion: v1
kind: Service
metadata:
  name: supabase-db
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-db
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 5432
      targetPort: 5432
      protocol: TCP
      name: postgres
  selector:
    app.kubernetes.io/name: supabase-db
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-db
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-db
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: database
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.kubernetes.io/name: supabase-db
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: supabase-db
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: database
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: postgres
          image: postgres:15-alpine
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 5432
              name: postgres
          env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: POSTGRES_PASSWORD
            - name: POSTGRES_DB
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_DB
            - name: POSTGRES_USER
              value: "postgres"
          volumeMounts:
            - name: db-data
              mountPath: /var/lib/postgresql/data
          livenessProbe:
            exec:
              command:
                - pg_isready
                - -U
                - postgres
                - -h
                - localhost
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 6
          readinessProbe:
            exec:
              command:
                - pg_isready
                - -U
                - postgres
                - -h
                - localhost
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
      volumes:
        - name: db-data
          persistentVolumeClaim:
            claimName: supabase-db-data
      restartPolicy: Always
