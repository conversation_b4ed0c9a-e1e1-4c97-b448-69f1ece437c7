---
apiVersion: v1
kind: Service
metadata:
  name: supabase-meta
  namespace: supabase
  labels:
    app.kubernetes.io/name: postgres-meta
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: meta
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: postgres-meta
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-meta
  namespace: supabase
  labels:
    app.kubernetes.io/name: postgres-meta
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: meta
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: postgres-meta
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: postgres-meta
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: meta
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: postgres-meta
          image: supabase/postgres-meta:v0.89.3
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8080
              name: http
          env:
            - name: PG_META_PORT
              value: "8080"
            - name: PG_META_DB_HOST
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_HOST
            - name: PG_META_DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_PORT
            - name: PG_META_DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_DB
            - name: PG_META_DB_USER
              value: "supabase_admin"
            - name: PG_META_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: POSTGRES_PASSWORD
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
      restartPolicy: Always
