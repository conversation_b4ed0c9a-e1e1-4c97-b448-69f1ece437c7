---
apiVersion: v1
kind: Service
metadata:
  name: supabase-analytics
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-analytics
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: analytics
    app.kubernetes.io/part-of: supabase
spec:
  type: ClusterIP
  ports:
    - port: 4000
      targetPort: 4000
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: supabase-analytics
    app.kubernetes.io/instance: supabase

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: supabase-analytics
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-analytics
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: analytics
    app.kubernetes.io/part-of: supabase
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: supabase-analytics
      app.kubernetes.io/instance: supabase
  template:
    metadata:
      labels:
        app.kubernetes.io/name: supabase-analytics
        app.kubernetes.io/instance: supabase
        app.kubernetes.io/component: analytics
        app.kubernetes.io/part-of: supabase
    spec:
      containers:
        - name: logflare
          image: supabase/logflare:1.14.2
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 4000
              name: http
          env:
            - name: LOGFLARE_NODE_HOST
              value: "127.0.0.1"
            - name: DB_USERNAME
              value: "supabase_admin"
            - name: DB_DATABASE
              value: "_supabase"
            - name: DB_HOSTNAME
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_HOST
            - name: DB_PORT
              valueFrom:
                configMapKeyRef:
                  name: supabase-config
                  key: POSTGRES_PORT
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: POSTGRES_PASSWORD
            - name: DB_SCHEMA
              value: "_analytics"
            - name: LOGFLARE_PUBLIC_ACCESS_TOKEN
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: LOGFLARE_PUBLIC_ACCESS_TOKEN
            - name: LOGFLARE_PRIVATE_ACCESS_TOKEN
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: LOGFLARE_PRIVATE_ACCESS_TOKEN
            - name: LOGFLARE_SINGLE_TENANT
              value: "true"
            - name: LOGFLARE_SUPABASE_MODE
              value: "true"
            - name: LOGFLARE_MIN_CLUSTER_SIZE
              value: "1"
            - name: POSTGRES_BACKEND_URL
              value: "postgresql://supabase_admin:$(POSTGRES_PASSWORD)@$(POSTGRES_HOST):$(POSTGRES_PORT)/_supabase"
            - name: POSTGRES_BACKEND_SCHEMA
              value: "_analytics"
            - name: LOGFLARE_FEATURE_FLAG_OVERRIDE
              value: "multibackend=true"
          livenessProbe:
            httpGet:
              path: /health
              port: 4000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /health
              port: 4000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
      restartPolicy: Always
