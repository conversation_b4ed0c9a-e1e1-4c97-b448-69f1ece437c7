apiVersion: v1
kind: Secret
metadata:
  name: supabase-secrets
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: supabase
type: Opaque
stringData:
  # Database credentials
  POSTGRES_PASSWORD: "your-super-secret-and-long-postgres-password"
  
  # JWT secrets
  JWT_SECRET: "your-super-secret-jwt-token-with-at-least-32-characters-long"
  ANON_KEY: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE"
  SERVICE_ROLE_KEY: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJzZXJ2aWNlX3JvbGUiLAogICAgImlzcyI6ICJzdXBhYmFzZS1kZW1vIiwKICAgICJpYXQiOiAxNjQxNzY5MjAwLAogICAgImV4cCI6IDE3OTk1MzU2MDAKfQ.DaYlNEoUrrEn2Ig7tqibS-PHK5vgusbcbo7X36XVt4Q"
  
  # Dashboard credentials
  DASHBOARD_USERNAME: "supabase"
  DASHBOARD_PASSWORD: "this_password_is_insecure_and_should_be_updated"
  
  # Encryption keys
  SECRET_KEY_BASE: "UpNVntn3cDxHJpq99YMc1T1AQgQpc8kfYTuRgBiYa15BLrx8etQoXz3gZv1/u2oq"
  VAULT_ENC_KEY: "your-encryption-key-32-chars-min"
  
  # Logflare tokens
  LOGFLARE_PUBLIC_ACCESS_TOKEN: "your-super-secret-and-long-logflare-key-public"
  LOGFLARE_PRIVATE_ACCESS_TOKEN: "your-super-secret-and-long-logflare-key-private"
  
  # SMTP configuration
  SMTP_ADMIN_EMAIL: "<EMAIL>"
  SMTP_HOST: "supabase-mail"
  SMTP_PORT: "2500"
  SMTP_USER: "fake_mail_user"
  SMTP_PASS: "fake_mail_password"
  SMTP_SENDER_NAME: "fake_sender"
  
  # Optional: OpenAI API key for SQL Editor Assistant
  OPENAI_API_KEY: ""
  
  # Google Cloud credentials (for BigQuery analytics backend)
  GOOGLE_PROJECT_ID: "GOOGLE_PROJECT_ID"
  GOOGLE_PROJECT_NUMBER: "GOOGLE_PROJECT_NUMBER"
