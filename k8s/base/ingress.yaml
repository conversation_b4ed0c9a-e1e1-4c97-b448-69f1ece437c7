---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: supabase-ingress
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase-ingress
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: ingress
    app.kubernetes.io/part-of: supabase
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "300"
    # Enable WebSocket support for Realtime
    nginx.ingress.kubernetes.io/proxy-set-headers: |
      Upgrade $http_upgrade
      Connection "upgrade"
    # CORS headers
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,apikey,x-client-info"
spec:
  ingressClassName: nginx
  rules:
    - host: supabase.local
      http:
        paths:
          # Main API Gateway (Kong) - handles all API routes
          - path: /
            pathType: Prefix
            backend:
              service:
                name: supabase-kong
                port:
                  number: 8000
    - host: studio.supabase.local
      http:
        paths:
          # Direct access to Studio (Dashboard)
          - path: /
            pathType: Prefix
            backend:
              service:
                name: supabase-studio
                port:
                  number: 3000
    - host: analytics.supabase.local
      http:
        paths:
          # Direct access to Analytics (Logflare)
          - path: /
            pathType: Prefix
            backend:
              service:
                name: supabase-analytics
                port:
                  number: 4000

---
# Alternative Ingress for SSL/TLS termination (uncomment and configure as needed)
# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: supabase-ingress-tls
#   namespace: supabase
#   labels:
#     app.kubernetes.io/name: supabase-ingress-tls
#     app.kubernetes.io/instance: supabase
#     app.kubernetes.io/component: ingress
#     app.kubernetes.io/part-of: supabase
#   annotations:
#     nginx.ingress.kubernetes.io/ssl-redirect: "true"
#     nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
#     cert-manager.io/cluster-issuer: "letsencrypt-prod"
# spec:
#   ingressClassName: nginx
#   tls:
#     - hosts:
#         - supabase.yourdomain.com
#         - studio.supabase.yourdomain.com
#         - analytics.supabase.yourdomain.com
#       secretName: supabase-tls
#   rules:
#     - host: supabase.yourdomain.com
#       http:
#         paths:
#           - path: /
#             pathType: Prefix
#             backend:
#               service:
#                 name: supabase-kong
#                 port:
#                   number: 8000
#     - host: studio.supabase.yourdomain.com
#       http:
#         paths:
#           - path: /
#             pathType: Prefix
#             backend:
#               service:
#                 name: supabase-studio
#                 port:
#                   number: 3000
#     - host: analytics.supabase.yourdomain.com
#       http:
#         paths:
#           - path: /
#             pathType: Prefix
#             backend:
#               service:
#                 name: supabase-analytics
#                 port:
#                   number: 4000
