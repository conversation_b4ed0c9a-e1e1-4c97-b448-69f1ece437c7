apiVersion: v1
kind: ConfigMap
metadata:
  name: supabase-config
  namespace: supabase
  labels:
    app.kubernetes.io/name: supabase
    app.kubernetes.io/instance: supabase
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: supabase
data:
  # Database configuration
  POSTGRES_HOST: "supabase-db"
  POSTGRES_DB: "postgres"
  POSTGRES_PORT: "5432"
  
  # API Proxy configuration
  KONG_HTTP_PORT: "8000"
  KONG_HTTPS_PORT: "8443"
  
  # API configuration
  PGRST_DB_SCHEMAS: "public,storage,graphql_public"
  
  # Auth configuration
  SITE_URL: "http://localhost:3000"
  ADDITIONAL_REDIRECT_URLS: ""
  JWT_EXPIRY: "3600"
  DISABLE_SIGNUP: "false"
  API_EXTERNAL_URL: "http://localhost:8000"
  
  # Mailer configuration
  MAILER_URLPATHS_CONFIRMATION: "/auth/v1/verify"
  MAILER_URLPATHS_INVITE: "/auth/v1/verify"
  MAILER_URLPATHS_RECOVERY: "/auth/v1/verify"
  MAILER_URLPATHS_EMAIL_CHANGE: "/auth/v1/verify"
  
  # Email auth configuration
  ENABLE_EMAIL_SIGNUP: "true"
  ENABLE_EMAIL_AUTOCONFIRM: "false"
  
  # Phone auth configuration
  ENABLE_PHONE_SIGNUP: "true"
  ENABLE_PHONE_AUTOCONFIRM: "true"
  ENABLE_ANONYMOUS_USERS: "false"
  
  # Studio configuration
  STUDIO_DEFAULT_ORGANIZATION: "Default Organization"
  STUDIO_DEFAULT_PROJECT: "Default Project"
  STUDIO_PORT: "3000"
  SUPABASE_PUBLIC_URL: "http://localhost:8000"
  
  # Image proxy configuration
  IMGPROXY_ENABLE_WEBP_DETECTION: "true"
  
  # Functions configuration
  FUNCTIONS_VERIFY_JWT: "false"
  
  # Supavisor configuration
  POOLER_PROXY_PORT_TRANSACTION: "6543"
  POOLER_DEFAULT_POOL_SIZE: "20"
  POOLER_MAX_CLIENT_CONN: "100"
  POOLER_TENANT_ID: "your-tenant-id"
  POOLER_DB_POOL_SIZE: "5"
