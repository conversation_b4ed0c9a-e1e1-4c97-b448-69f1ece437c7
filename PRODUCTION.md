# Production Deployment Guide

This guide covers the additional considerations and configurations needed for deploying Supabase in a production Kubernetes environment.

## Security Hardening

### 1. Update Default Secrets

**Critical**: Change all default passwords and secrets before production deployment.

Generate secure secrets:

```bash
# Generate a secure PostgreSQL password
POSTGRES_PASSWORD=$(openssl rand -base64 32)

# Generate a secure JWT secret (minimum 32 characters)
JWT_SECRET=$(openssl rand -base64 48)

# Generate other secrets
SECRET_KEY_BASE=$(openssl rand -base64 64)
VAULT_ENC_KEY=$(openssl rand -base64 32)
DASHBOARD_PASSWORD=$(openssl rand -base64 16)
LOGFLARE_PUBLIC_TOKEN=$(openssl rand -base64 32)
LOGFLARE_PRIVATE_TOKEN=$(openssl rand -base64 32)
```

Update the secrets in `k8s/base/secrets.yaml` with these generated values.

### 2. Generate New JWT Keys

The default ANON_KEY and SERVICE_ROLE_KEY are for demo purposes only. Generate new ones:

```bash
# Install Supabase CLI
npm install -g supabase

# Generate new JWT keys
supabase gen keys --anon-key --service-key --jwt-secret "$JWT_SECRET"
```

### 3. Network Security

Create network policies to restrict inter-pod communication:

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: supabase-network-policy
  namespace: supabase
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: supabase
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: supabase
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
```

### 4. RBAC Configuration

Create service accounts with minimal required permissions:

```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: supabase-service-account
  namespace: supabase
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: supabase-role
  namespace: supabase
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: supabase-role-binding
  namespace: supabase
subjects:
- kind: ServiceAccount
  name: supabase-service-account
  namespace: supabase
roleRef:
  kind: Role
  name: supabase-role
  apiGroup: rbac.authorization.k8s.io
```

## High Availability

### 1. PostgreSQL High Availability

For production, consider using a managed PostgreSQL service or set up PostgreSQL clustering:

#### Option A: Managed Database
Update the database connection in `k8s/base/configmap.yaml`:

```yaml
data:
  POSTGRES_HOST: "your-managed-db-host"
  POSTGRES_PORT: "5432"
  POSTGRES_DB: "your-database-name"
```

#### Option B: PostgreSQL Cluster
Use an operator like PostgreSQL Operator or Zalando Postgres Operator:

```bash
# Install PostgreSQL Operator
kubectl apply -k github.com/zalando/postgres-operator/manifests
```

### 2. Service Replicas

Increase replicas for stateless services:

```yaml
# In each service deployment
spec:
  replicas: 3  # Increase from 1
```

### 3. Pod Disruption Budgets

Create PDBs to ensure availability during updates:

```yaml
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: supabase-auth-pdb
  namespace: supabase
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: supabase-auth
```

## Resource Management

### 1. Resource Requests and Limits

Update resource specifications based on your workload:

```yaml
resources:
  requests:
    memory: "512Mi"
    cpu: "250m"
  limits:
    memory: "2Gi"
    cpu: "1000m"
```

### 2. Horizontal Pod Autoscaling

Enable HPA for services that can scale horizontally:

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: supabase-auth-hpa
  namespace: supabase
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: supabase-auth
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### 3. Vertical Pod Autoscaling

For services that need vertical scaling:

```yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: supabase-db-vpa
  namespace: supabase
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: supabase-db
  updatePolicy:
    updateMode: "Auto"
```

## Storage Configuration

### 1. Production Storage Classes

Use appropriate storage classes for production:

```yaml
# For database storage
storageClassName: fast-ssd  # High IOPS for database

# For object storage
storageClassName: standard  # Standard storage for files
```

### 2. Backup Strategy

Implement automated backups:

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: supabase
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            command:
            - /bin/sh
            - -c
            - |
              pg_dump -h $POSTGRES_HOST -U postgres -d $POSTGRES_DB > /backup/backup-$(date +%Y%m%d-%H%M%S).sql
            env:
            - name: POSTGRES_HOST
              value: "supabase-db"
            - name: POSTGRES_DB
              value: "postgres"
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: supabase-secrets
                  key: POSTGRES_PASSWORD
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
```

## SSL/TLS Configuration

### 1. Certificate Management

Install cert-manager for automatic certificate management:

```bash
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml
```

Create a ClusterIssuer:

```yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
```

### 2. Update Ingress for TLS

Update the ingress configuration to use TLS:

```yaml
spec:
  tls:
  - hosts:
    - supabase.yourdomain.com
    - studio.supabase.yourdomain.com
    secretName: supabase-tls
  rules:
  - host: supabase.yourdomain.com
    # ... rest of configuration
```

## Monitoring and Observability

### 1. Prometheus and Grafana

Install monitoring stack:

```bash
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install prometheus prometheus-community/kube-prometheus-stack -n monitoring --create-namespace
```

### 2. Service Monitors

Create ServiceMonitor resources for Prometheus:

```yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: supabase-services
  namespace: supabase
spec:
  selector:
    matchLabels:
      app.kubernetes.io/part-of: supabase
  endpoints:
  - port: http
    path: /metrics
```

### 3. Alerting Rules

Configure alerting for critical issues:

```yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: supabase-alerts
  namespace: supabase
spec:
  groups:
  - name: supabase
    rules:
    - alert: SupabaseServiceDown
      expr: up{job="supabase-services"} == 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: "Supabase service is down"
        description: "{{ $labels.instance }} has been down for more than 5 minutes"
```

## Performance Optimization

### 1. Database Optimization

Configure PostgreSQL for production:

```sql
-- Increase shared_buffers (25% of RAM)
ALTER SYSTEM SET shared_buffers = '2GB';

-- Increase effective_cache_size (75% of RAM)
ALTER SYSTEM SET effective_cache_size = '6GB';

-- Optimize for concurrent connections
ALTER SYSTEM SET max_connections = 200;

-- Enable query optimization
ALTER SYSTEM SET random_page_cost = 1.1;

SELECT pg_reload_conf();
```

### 2. Connection Pooling

Configure Supavisor for optimal connection pooling:

```yaml
env:
- name: POOLER_DEFAULT_POOL_SIZE
  value: "25"
- name: POOLER_MAX_CLIENT_CONN
  value: "1000"
```

### 3. Caching

Consider adding Redis for caching:

```bash
helm repo add bitnami https://charts.bitnami.com/bitnami
helm install redis bitnami/redis -n supabase
```

## Disaster Recovery

### 1. Multi-Region Setup

For disaster recovery, consider deploying across multiple regions with database replication.

### 2. Backup and Restore Procedures

Document and test backup/restore procedures:

```bash
# Backup
kubectl exec -it supabase-db-0 -n supabase -- pg_dump -U postgres postgres > backup.sql

# Restore
kubectl exec -i supabase-db-0 -n supabase -- psql -U postgres postgres < backup.sql
```

### 3. Configuration Backup

Backup Kubernetes configurations:

```bash
kubectl get all,configmaps,secrets,pvc -n supabase -o yaml > supabase-backup.yaml
```

## Maintenance

### 1. Update Strategy

Use rolling updates with proper readiness probes:

```yaml
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
```

### 2. Health Checks

Ensure all services have proper health checks configured.

### 3. Log Management

Configure log rotation and retention policies for persistent logs.

This production guide should be customized based on your specific requirements, infrastructure, and compliance needs.
