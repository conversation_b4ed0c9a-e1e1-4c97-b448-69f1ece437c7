# Self-Hosted Supabase on Kubernetes

This repository contains Kubernetes deployment manifests for running a self-hosted Supabase stack. Supabase is an open-source Firebase alternative providing a PostgreSQL database, authentication, instant APIs, realtime subscriptions, and storage.

## Architecture

The Supabase stack consists of the following components:

- **PostgreSQL**: The core database
- **GoTrue**: User authentication and management
- **PostgREST**: RESTful API for PostgreSQL
- **Realtime**: Real-time subscriptions
- **Storage**: Object storage API
- **Edge Functions**: Serverless functions
- **Studio**: Admin dashboard
- **Kong**: API Gateway
- **Logflare**: Analytics and logging
- **Vector**: Log collection
- **Supavisor**: Connection pooler
- **Postgres Meta**: Database metadata API
- **imgproxy**: Image transformation service

## Prerequisites

- Kubernetes cluster (v1.19+)
- kubectl configured to access your cluster
- Persistent storage support in your cluster
- Ingress controller (optional, for external access)
- <PERSON><PERSON> (optional, for additional components)

## Deployment

### Quick Start

1. Clone this repository:
   ```bash
   git clone https://github.com/yourusername/supabase-kubernetes.git
   cd supabase-kubernetes
   ```

2. Review and modify configuration:
   - Update secrets in `k8s/base/secrets.yaml`
   - Adjust configuration in `k8s/base/configmap.yaml`
   - Modify storage classes in PVC definitions if needed

3. Run the deployment script:
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

### Manual Deployment

If you prefer to deploy components manually:

1. Create namespace and basic configuration:
   ```bash
   kubectl apply -f k8s/base/namespace.yaml
   kubectl apply -f k8s/base/secrets.yaml
   kubectl apply -f k8s/base/configmap.yaml
   ```

2. Deploy Vector (logging):
   ```bash
   kubectl apply -f k8s/services/vector.yaml
   ```

3. Deploy PostgreSQL:
   ```bash
   kubectl apply -f k8s/services/postgres.yaml
   ```

4. Initialize PostgreSQL:
   ```bash
   kubectl apply -f k8s/services/postgres-init/init-configmap.yaml
   kubectl apply -f k8s/services/postgres-init/init-job.yaml
   ```

5. Deploy Analytics:
   ```bash
   kubectl apply -f k8s/services/analytics.yaml
   ```

6. Deploy remaining services:
   ```bash
   kubectl apply -f k8s/services/auth.yaml
   kubectl apply -f k8s/services/rest.yaml
   kubectl apply -f k8s/services/storage.yaml
   kubectl apply -f k8s/services/realtime.yaml
   kubectl apply -f k8s/services/meta.yaml
   kubectl apply -f k8s/services/studio.yaml
   kubectl apply -f k8s/services/functions.yaml
   kubectl apply -f k8s/services/supavisor.yaml
   kubectl apply -f k8s/services/kong.yaml
   ```

7. Deploy ingress (optional):
   ```bash
   kubectl apply -f k8s/base/ingress.yaml
   ```

## Configuration

### Secrets

Update the following secrets in `k8s/base/secrets.yaml` before deployment:

- `POSTGRES_PASSWORD`: PostgreSQL database password
- `JWT_SECRET`: Secret for JWT token generation
- `ANON_KEY`: Anonymous API key
- `SERVICE_ROLE_KEY`: Service role API key
- `DASHBOARD_USERNAME`: Studio dashboard username
- `DASHBOARD_PASSWORD`: Studio dashboard password
- `SECRET_KEY_BASE`: Secret key for encryption
- `VAULT_ENC_KEY`: Encryption key for vault
- `LOGFLARE_PUBLIC_ACCESS_TOKEN`: Public token for Logflare
- `LOGFLARE_PRIVATE_ACCESS_TOKEN`: Private token for Logflare

### Environment Variables

Adjust environment variables in `k8s/base/configmap.yaml` to customize your deployment:

- Database configuration
- API settings
- Authentication options
- Email and SMS settings
- Studio configuration
- Connection pooler settings

### Storage

By default, the deployment uses `hostpath` storage class for persistence. For production, you should use a more robust storage class:

1. Update the `storageClassName` in all PVC definitions:
   - `k8s/services/postgres.yaml`
   - `k8s/services/storage.yaml`
   - `k8s/services/functions.yaml`

2. Ensure your storage class supports the access modes required by each service.

### Ingress

The default ingress configuration in `k8s/base/ingress.yaml` assumes:

- An NGINX ingress controller
- Local domain names (`supabase.local`, `studio.supabase.local`, `analytics.supabase.local`)

For production use:

1. Update the host names to your actual domain
2. Uncomment and configure the TLS section for HTTPS
3. Install cert-manager for automatic certificate management

## Accessing Services

After deployment, you can access the services:

- **API Gateway**: http://supabase.local or http://CLUSTER_IP:8000
- **Studio Dashboard**: http://studio.supabase.local or http://CLUSTER_IP:8000
- **Analytics**: http://analytics.supabase.local or http://CLUSTER_IP:4000

Default credentials:
- **Dashboard Username**: supabase
- **Dashboard Password**: this_password_is_insecure_and_should_be_updated

## Production Considerations

For production deployments:

1. **Security**:
   - Change all default passwords and secrets
   - Enable TLS for all services
   - Use proper network policies
   - Implement proper RBAC

2. **High Availability**:
   - Set up PostgreSQL replication
   - Increase replicas for stateless services
   - Use node affinity and pod anti-affinity
   - Implement proper backup strategies

3. **Monitoring**:
   - Set up Prometheus and Grafana
   - Configure proper resource limits and requests
   - Implement alerting

4. **Scaling**:
   - Adjust resource requests and limits
   - Configure horizontal pod autoscaling
   - Consider vertical scaling for database

## Verification

After deployment, verify that all services are running:

```bash
kubectl get pods -n supabase
kubectl get services -n supabase
kubectl get ingress -n supabase
```

Test the API endpoints:

```bash
# Test the API Gateway
curl http://supabase.local/rest/v1/

# Test authentication
curl -X POST http://supabase.local/auth/v1/signup \
  -H "Content-Type: application/json" \
  -H "apikey: YOUR_ANON_KEY" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

## Troubleshooting

### Common Issues

1. **PostgreSQL fails to start**:
   - Check PVC provisioning: `kubectl get pvc -n supabase`
   - Verify storage class compatibility
   - Check resource limits and node capacity
   - Review logs: `kubectl logs -l app.kubernetes.io/name=supabase-db -n supabase`

2. **Services can't connect to PostgreSQL**:
   - Verify PostgreSQL is running: `kubectl get pods -l app.kubernetes.io/name=supabase-db -n supabase`
   - Check initialization job completed: `kubectl get jobs -n supabase`
   - Verify network policies allow communication
   - Check service DNS resolution

3. **Authentication issues**:
   - Verify JWT_SECRET is consistent across all services
   - Check GoTrue configuration and logs
   - Verify database roles are set up correctly
   - Test database connectivity from auth service

4. **Ingress not working**:
   - Verify ingress controller is installed
   - Check ingress class configuration
   - Update /etc/hosts for local testing
   - Verify DNS resolution for production

### Logs

To check logs for a specific service:

```bash
kubectl logs -f -l app.kubernetes.io/name=<service-name> -n supabase
```

Service names:
- `supabase-db` - PostgreSQL database
- `supabase-auth` - GoTrue authentication
- `postgrest` - PostgREST API
- `supabase-realtime` - Realtime subscriptions
- `supabase-storage` - Object storage
- `supabase-studio` - Admin dashboard
- `kong` - API Gateway
- `supabase-analytics` - Logflare analytics
- `vector` - Log collection
- `supavisor` - Connection pooler

### Health Checks

Check service health:

```bash
# Check all pod status
kubectl get pods -n supabase -o wide

# Check service endpoints
kubectl get endpoints -n supabase

# Port forward to test individual services
kubectl port-forward svc/supabase-kong 8000:8000 -n supabase
```

## Cleanup

To remove the entire Supabase deployment:

```bash
kubectl delete namespace supabase
```

Or remove individual components:

```bash
kubectl delete -f k8s/services/
kubectl delete -f k8s/base/
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Supabase](https://supabase.com/) for creating an amazing open-source platform
- The Kubernetes community for best practices and patterns
